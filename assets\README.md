# 资源文件目录

这个目录用于存放应用程序的静态资源文件。

## 目录结构

### `images/` - 图片资源
- 存放应用中使用的图片文件
- 支持格式：PNG, JPG, JPEG, GIF, WebP
- 建议使用PNG格式以获得最佳质量

### `icons/` - 图标资源  
- 存放应用中使用的图标文件
- 建议使用SVG或PNG格式
- 可以包含不同尺寸的图标

## 使用方法

在Flutter代码中使用资源文件：

```dart
// 使用图片
Image.asset('assets/images/your_image.png')

// 使用图标
Image.asset('assets/icons/your_icon.png')
```

## 注意事项

1. 添加新资源文件后，需要运行 `flutter pub get` 来更新资源索引
2. 资源文件名建议使用小写字母和下划线
3. 避免使用中文文件名
4. 大图片建议压缩后再使用，以减少应用体积

## 常用资源类型

- **Logo**: 应用程序标志
- **背景图**: 页面背景图片
- **按钮图标**: 功能按钮的图标
- **装饰图片**: 界面装饰用图片
- **用户头像**: 默认头像图片
